import React, { useState } from 'react';
import { Trash2, ThumbsUp, ThumbsDown, MessageSquare } from 'lucide-react';

interface Complaint {
  id: number;
  created_at: string;
  complaint_text: string;
  user_id: string;
  organization_id: string;
  comments?: Comment[];
  reactions?: Reaction[];
}

interface Comment {
  id: number;
  text: string;
  user_id: string;
  created_at: string;
}

interface Reaction {
  id: number;
  type: 'like' | 'dislike';
  user_id: string;
}

const SuperAdminComplaint: React.FC = () => {
  // Mock user ID for demonstration
  const currentUserId = "current-user-id";

  // Initial complaints data from Supabase
  const initialComplaints = [
    {
      id: 23,
      created_at: "2025-04-07T11:51:11.052768+00:00",
      complaint_text: "Testing complaints.",
      user_id: "593442f7-95db-42f2-a3b4-bc21e48ad4f6",
      organization_id: "e05c087b-313e-4668-bb14-a5ef5c6e2e2d",
      comments: [],
      reactions: []
    },
    {
      id: 25,
      created_at: "2025-07-04T05:24:44.500184+00:00",
      complaint_text: "Subject: Wi-Fi Connectivity Issue Dear HR I hope this message finds you well. I would like to bring to your attention that the office Wi-Fi has not been working properly.",
      user_id: "859fd4a4-fb7a-42dc-8643-ae539e7f67f8",
      organization_id: "e05c087b-313e-4668-bb14-a5ef5c6e2e2d",
      comments: [],
      reactions: []
    },
    {
      id: 26,
      created_at: "2025-07-04T05:44:22.150908+00:00",
      complaint_text: "Subject: Wi-Fi Connectivity IssueDear HR I hope this message finds you well. I would like to bring to your attention that the office Wi-Fi has not been working properly.",
      user_id: "0ca52ca0-5b18-49c9-af87-bb357b42b739",
      organization_id: "e05c087b-313e-4668-bb14-a5ef5c6e2e2d",
      comments: [],
      reactions: []
    }
  ];

  const [complaints, setComplaints] = useState<Complaint[]>(initialComplaints);
  const [newComments, setNewComments] = useState<{[key: number]: string}>({});

  // Format date to be more readable
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // Handle adding a comment to a complaint
  const handleAddComment = (complaintId: number) => {
    if (!newComments[complaintId] || newComments[complaintId].trim() === '') return;
    
    const updatedComplaints = complaints.map(complaint => {
      if (complaint.id === complaintId) {
        const newComment = {
          id: Date.now(), // Generate a temporary ID
          text: newComments[complaintId],
          user_id: currentUserId,
          created_at: new Date().toISOString()
        };
        return {
          ...complaint,
          comments: [...(complaint.comments || []), newComment]
        };
      }
      return complaint;
    });
    
    setComplaints(updatedComplaints);
    setNewComments({...newComments, [complaintId]: ''});
    
    // Here you would typically make an API call to save the comment to your database
  };

  // Handle adding a reaction to a complaint
  const handleReaction = (complaintId: number, reactionType: 'like' | 'dislike') => {
    const updatedComplaints = complaints.map(complaint => {
      if (complaint.id === complaintId) {
        // Check if user already reacted
        const existingReactionIndex = complaint.reactions?.findIndex(
          r => r.user_id === currentUserId
        );
        
        let updatedReactions = [...(complaint.reactions || [])];
        
        if (existingReactionIndex !== undefined && existingReactionIndex >= 0) {
          // If same reaction type, remove it (toggle off)
          if (updatedReactions[existingReactionIndex].type === reactionType) {
            updatedReactions.splice(existingReactionIndex, 1);
          } else {
            // Change reaction type
            updatedReactions[existingReactionIndex].type = reactionType;
          }
        } else {
          // Add new reaction
          updatedReactions.push({
            id: Date.now(),
            type: reactionType,
            user_id: currentUserId
          });
        }
        
        return {
          ...complaint,
          reactions: updatedReactions
        };
      }
      return complaint;
    });
    
    setComplaints(updatedComplaints);
    
    // Here you would typically make an API call to save the reaction to your database
  };

  // Handle deleting a complaint
  const handleDeleteComplaint = (complaintId: number) => {
    const updatedComplaints = complaints.filter(complaint => complaint.id !== complaintId);
    setComplaints(updatedComplaints);
    
    // Here you would typically make an API call to delete the complaint from your database
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Software Complaints</h1>
      
      <div className="space-y-6">
        {complaints.map(complaint => (
          <div key={complaint.id} className="bg-white shadow-md rounded-lg p-4 border border-gray-200">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-gray-500 mb-2">{formatDate(complaint.created_at)}</p>
                <p className="text-gray-800">{complaint.complaint_text}</p>
              </div>
              <button 
                onClick={() => handleDeleteComplaint(complaint.id)}
                className="text-red-500 hover:text-red-700"
                title="Delete complaint"
              >
                <Trash2 size={18} />
              </button>
            </div>
            
            {/* Reactions */}
            <div className="flex gap-4 mt-4">
              <button 
                onClick={() => handleReaction(complaint.id, 'like')}
                className={`flex items-center gap-1 ${complaint.reactions?.some(r => r.user_id === currentUserId && r.type === 'like') ? 'text-blue-600' : 'text-gray-500'}`}
              >
                <ThumbsUp size={16} />
                <span>{complaint.reactions?.filter(r => r.type === 'like').length || 0}</span>
              </button>
              
              <button 
                onClick={() => handleReaction(complaint.id, 'dislike')}
                className={`flex items-center gap-1 ${complaint.reactions?.some(r => r.user_id === currentUserId && r.type === 'dislike') ? 'text-red-600' : 'text-gray-500'}`}
              >
                <ThumbsDown size={16} />
                <span>{complaint.reactions?.filter(r => r.type === 'dislike').length || 0}</span>
              </button>
              
              <div className="flex items-center gap-1 text-gray-500">
                <MessageSquare size={16} />
                <span>{complaint.comments?.length || 0}</span>
              </div>
            </div>
            
            {/* Comments section */}
            <div className="mt-4">
              {complaint.comments && complaint.comments.length > 0 && (
                <div className="border-t pt-3 mt-3">
                  <h3 className="text-sm font-medium mb-2">Comments</h3>
                  <div className="space-y-2">
                    {complaint.comments.map(comment => (
                      <div key={comment.id} className="bg-gray-50 p-2 rounded text-sm">
                        <p className="text-gray-800">{comment.text}</p>
                        <p className="text-xs text-gray-500 mt-1">{formatDate(comment.created_at)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Add comment form */}
              <div className="mt-3 flex">
                <input
                  type="text"
                  value={newComments[complaint.id] || ''}
                  onChange={(e) => setNewComments({...newComments, [complaint.id]: e.target.value})}
                  placeholder="Add a comment..."
                  className="flex-grow border rounded-l px-3 py-1 text-sm"
                />
                <button
                  onClick={() => handleAddComment(complaint.id)}
                  className="bg-blue-500 text-white px-3 py-1 rounded-r text-sm hover:bg-blue-600"
                >
                  Post
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SuperAdminComplaint;